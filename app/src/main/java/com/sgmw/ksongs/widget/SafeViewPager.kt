package com.sgmw.ksongs.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 * 安全的ViewPager，根本解决多指滑动时的crash问题
 * 主要解决：java.lang.IllegalArgumentException: pointerIndex out of range
 *
 * 解决方案：
 * 1. 预防性检查：在处理事件前验证指针索引有效性
 * 2. 智能降级：当检测到无效状态时，转换为单指事件处理
 * 3. 异常兜底：作为最后的保险机制
 */
class SafeViewPager @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ViewPager(context, attrs) {

    /**
     * 检查MotionEvent的有效性，防止指针索引越界
     */
    private fun isMotionEventValid(ev: MotionEvent?): Boolean {
        if (ev == null) return false

        val action = ev.action
        val actionMasked = action and MotionEvent.ACTION_MASK
        val pointerIndex = (action and MotionEvent.ACTION_POINTER_INDEX_MASK) shr MotionEvent.ACTION_POINTER_INDEX_SHIFT
        val pointerCount = ev.pointerCount

        // 检查指针索引是否在有效范围内
        return pointerIndex < pointerCount && pointerIndex >= 0
    }

    /**
     * 创建一个安全的单指MotionEvent，用于降级处理
     */
    private fun createSafeSinglePointerEvent(ev: MotionEvent): MotionEvent? {
        return try {
            // 只保留第一个指针的信息，创建单指事件
            MotionEvent.obtain(
                ev.downTime,
                ev.eventTime,
                MotionEvent.ACTION_CANCEL, // 使用CANCEL动作来安全结束当前手势
                ev.getX(0),
                ev.getY(0),
                0
            )
        } catch (e: Exception) {
            null
        }
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): Boolean {
        // 第一层防护：预防性检查
        if (!isMotionEventValid(ev)) {
            // 如果事件无效，尝试创建安全的单指事件进行降级处理
            val safeEvent = ev?.let { createSafeSinglePointerEvent(it) }
            return if (safeEvent != null) {
                try {
                    val result = super.onInterceptTouchEvent(safeEvent)
                    safeEvent.recycle()
                    result
                } catch (e: Exception) {
                    safeEvent.recycle()
                    false
                }
            } else {
                false
            }
        }

        // 第二层防护：异常捕获（作为兜底机制）
        return try {
            super.onInterceptTouchEvent(ev)
        } catch (ex: IllegalArgumentException) {
            // 记录异常信息用于调试（生产环境可以移除）
            // Log.w("SafeViewPager", "Caught IllegalArgumentException in onInterceptTouchEvent", ex)
            false
        } catch (ex: ArrayIndexOutOfBoundsException) {
            // Log.w("SafeViewPager", "Caught ArrayIndexOutOfBoundsException in onInterceptTouchEvent", ex)
            false
        }
    }

    override fun onTouchEvent(ev: MotionEvent?): Boolean {
        // 第一层防护：预防性检查
        if (!isMotionEventValid(ev)) {
            // 如果事件无效，尝试创建安全的单指事件进行降级处理
            val safeEvent = ev?.let { createSafeSinglePointerEvent(it) }
            return if (safeEvent != null) {
                try {
                    val result = super.onTouchEvent(safeEvent)
                    safeEvent.recycle()
                    result
                } catch (e: Exception) {
                    safeEvent.recycle()
                    false
                }
            } else {
                false
            }
        }

        // 第二层防护：异常捕获（作为兜底机制）
        return try {
            super.onTouchEvent(ev)
        } catch (ex: IllegalArgumentException) {
            // 记录异常信息用于调试（生产环境可以移除）
            // Log.w("SafeViewPager", "Caught IllegalArgumentException in onTouchEvent", ex)
            false
        } catch (ex: ArrayIndexOutOfBoundsException) {
            // Log.w("SafeViewPager", "Caught ArrayIndexOutOfBoundsException in onTouchEvent", ex)
            false
        }
    }
}
