<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_singing"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_80"
        android:background="@color/bg_play_list_playing_color"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingStart="@dimen/dp_40"
        android:paddingEnd="@dimen/dp_110"
        android:textColor="@color/font_play_list_playing_color"
        android:textSize="@dimen/sp_32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="RtlSymmetry"
        tools:text="正在播放：青花瓷" />

    <ImageView
        android:id="@+id/iv_singing"
        android:layout_width="@dimen/dp_64"
        android:layout_height="@dimen/dp_64"
        android:layout_marginEnd="@dimen/dp_40"
        android:background="@drawable/kw_animation_music_play_loop"
        android:importantForAccessibility="no"
        app:layout_constraintBottom_toBottomOf="@+id/tv_singing"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_singing" />

    <com.sgmw.common.widget.CustomRecyclerView
        android:id="@+id/rv_demand"
        android:layout_width="@dimen/dp_0"
        android:layout_height="@dimen/dp_0"
        android:layout_marginBottom="@dimen/dp_16"
        app:enableBottomFadeEffect="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_singing" />

</androidx.constraintlayout.widget.ConstraintLayout>